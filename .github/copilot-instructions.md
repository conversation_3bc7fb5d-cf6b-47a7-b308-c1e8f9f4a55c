# AI Agent Instructions: GPC Zoning Code Master

## Project Overview
This repository contains strategic planning tools for identifying, entitling, and monetizing under-developed land in East Baton Rouge Parish (EBR). The core focus is analyzing the intersection of **zoning districts**, **Future Land Use (FLU) categories**, and **character areas** to identify high-value development opportunities.

## Key Architecture & Data Flow

### Core Strategy Documents
- `ZONING_STRATEGY.md` - Comprehensive 3500+ line strategy guide with detailed FLU/zoning playbooks and financial models
- `ebr zoning.md` - Complete Unified Development Code (UDC) with administrative procedures and density requirements
- `ebr future land use categories.txt` - Definitions of all 13 FLU categories (AG/RU, CN, UN, DC, RC, EC, etc.)

### Critical Data Sources (CSV/)
- `Property_Information_20250716.csv` - 221K+ parcels with zoning, FLU, addresses, ownership (columns 47-48: improvement/land values)
- `Lot_20250716.csv` - 197K+ lot boundaries with character areas and overlay districts  
- `Building_Footprint_20250716.csv` - 197K+ building records for improvement value analysis

**Key Insight**: The CSV files contain 600K+ records - always use terminal commands (`head`, `grep`, `awk`, `cut`) for analysis rather than reading entire files.

## Financial Analysis Framework

### Under-Improved Ratio Analysis
Target parcels where **improvement value is substantially lower than land value**:
```bash
# Find parcels with improvement/land ratio < 0.5 (tear-down candidates)
awk -F',' '{if($47 && $48 && $48 > 0) ratio=$47/$48; if(ratio < 0.5) print $1,$47,$48,ratio}' Property_Information_20250716.csv

# High-value land with low improvements (best opportunities)
awk -F',' '{if($48 > 50000 && $47/$48 < 0.3) print $1,$11,$47,$48}' Property_Information_20250716.csv
```

### Pro Forma Components
Include these elements in financial feasibility analysis:
- **Acquisition cost** + **entitlement costs** ($500 + $100/acre + advertising)
- **Construction financing** with incentive equity layering
- **Exit values** via comparable sales or cap rate analysis
- **NPV calculation** factoring risk-adjusted returns and holding periods

### Incentive Stacking Models
Example downtown adaptive reuse project:
- **RTA**: 10-year property tax abatement
- **Historic Tax Credits**: 45% combined (20% federal + 25% state)
- **NMTC**: 39% credit over 7 years in qualified census tracts
- **Brownfield**: EPA grants for environmental remediation

## Complete Zoning District Guide

### Residential Districts
- **A1**: Rural residential, large lots (3+ acres) - Compatible with AG/RU FLU
- **A2**: Low-density suburban (5.8 du/ac) - Fits RN, may be under-intense for CN
- **A2.7**: Medium density (7.3 du/ac) - Suburban neighborhoods
- **A2.1**: Zero-lot-line residential - Suburban/compact neighborhoods
- **A2.9**: Duplex housing (2 units/lot) - Transition areas
- **A2.6**: Medium-density zero-lot-line (11.5 du/ac) - Upper-density suburban
- **A2.5**: Townhouse districts (up to 11.5 du/ac) - Compatible with CN FLU
- **A3.1**: Multi-family (11.5 du/ac) - CN and lower-intensity UN areas
- **A3.2**: Multi-family (17.4 du/ac) - Urban neighborhoods and mixed-use
- **A3.3**: High-density multi-family (29 du/ac) - UN and RC areas
- **A4**: Highest density (43.6 du/ac) - **Must be in Urban/Walkable character areas or Regional/Employment Centers**

### Neighborhood & Office Districts  
- **NO**: Neighborhood office - Small-scale professional services
- **NC**: Neighborhood commercial - Small retail serving residential areas
- **NC-AB**: Neighborhood commercial with alcohol service
- **GOL**: General office low-rise (≥50% office use) - 1-4 story professional buildings
- **GOH**: General office high-rise - Large office towers, mixed-use allowed

### Commercial Districts (Floor Area Limits)
- **LC1**: Light commercial (15,000 sq ft max/lot) - Local retail, small anchors
- **LC2**: Light commercial (75,000 sq ft max/lot) - Mid-scale shopping centers  
- **LC3**: Light commercial (150,000 sq ft max/lot) - Power centers, large retail
- **HC1**: Heavy commercial (250,000 sq ft max/lot) - Regional retail, big boxes
- **HC2**: Heavy commercial (unlimited floor area) - Major malls and centers
- **C5**: Downtown business district - **No setback/parking requirements**, mixed-use emphasis

### Industrial & Special Districts
- **CW1**: Commercial warehousing (50,000 sq ft max/lot) - Distribution centers
- **CW3**: Commercial warehousing (major arterials) - Logistics along 4-lane roads
- **M1**: Light industrial - Manufacturing, research labs, compatible with EC FLU
- **M2**: Heavy industrial - Major manufacturing, processing
- **C-AB-1/2**: Alcohol sales districts - Restaurants/bars with liquor licenses
- **CG**: Gaming districts - Casinos and gaming establishments
- **GA**: General Airport - Aviation-related uses only
- **X**: Adult business districts - Restricted adult entertainment uses

## Geographic Context & Priority Areas

### Downtown Core
- **Character**: Historic CBD, highest intensity, pedestrian-oriented
- **Key Corridors**: Government Street to Jefferson Highway
- **Development Focus**: Mixed-use towers, adaptive reuse, eliminate surface parking
- **Transportation**: Trolley system, future BRT connections, minimal parking requirements

### Major Commercial Corridors
- **Airline Highway**: Primary commercial strip, auto-oriented retail
- **Florida Boulevard**: Design overlay area, corridor revitalization focus
- **Jefferson Highway**: Office corridor with multiple overlay districts
- **Government Street**: Downtown extension with design standards
- **Bluebonnet Boulevard**: Suburban commercial with landscape requirements

### Transit-Oriented Development Areas
- **Plank Road Corridor**: $15M BUILD grant for Louisiana's first BRT system
- **BRT Route**: Connects North Baton Rouge to downtown and LSU
- **Planning Strategy**: Land banking of 80+ blighted parcels for catalytic projects
- **Opportunity**: Higher density, reduced parking near BRT stations

### Targeted Redevelopment Zones  
- **Scotlandville**: Community improvement plan integrated into FutureBR
- **Melrose East/Smiley Heights**: Ardendale Master Plan with 200-acre mixed-income development
- **Mid-City**: Brownfield remediation focus with EPA technical assistance
- **Choctaw/Zion City/Glen Oaks**: Community-driven improvement plans

### Employment Centers
- **Health District**: Southern parish hospital/medical cluster - encourage pedestrian design
- **Airport Area**: Transportation/logistics focus with large parcel requirements
- **LSU Campus**: Institutional area with student housing demand

## Character Area Zoning Compatibility

### Downtown Character Area
- **Preferred**: C5, GOH, A4 (high-density mixed-use)
- **Design Focus**: Street-level activation, no setbacks, structured parking

### Urban/Walkable Character Area  
- **Preferred**: A3.2-A4, LC1-LC3, GOL, NC (medium-density, transit-supportive)
- **Design Focus**: Connected streets, pedestrian infrastructure, mixed-use nodes

### Suburban Character Area
- **Preferred**: A2-A2.7, LC1-LC2, NC (auto-oriented, larger lots)
- **Design Focus**: Buffered development, shared parking, context-sensitive design

### Rural Character Area
- **Preferred**: A1, R, RE/A3 (agricultural preservation, cluster development)
- **Design Focus**: Large lots, minimal infrastructure, protect open space

## Development Workflow Patterns

### Data Analysis Commands
```bash
# Extract zoning/FLU combinations for analysis (use exact column positions)
grep -E "(A2|A3|C5|GOH)" CSV/Property_Information_20250716.csv | head -20

# Count parcels by FLU category (column 45)
cut -d',' -f45 CSV/Property_Information_20250716.csv | sort | uniq -c

# Find under-improved parcels (columns 47=improvement value, 48=land value)
awk -F',' 'NR>1 && $47 && $48 && $48>0 {ratio=$47/$48; if(ratio<0.5) print $1,$47,$48,ratio}' CSV/Property_Information_20250716.csv

# Character area distribution analysis (column 14 in Lot CSV)
cut -d',' -f14 CSV/Lot_20250716.csv | sort | uniq -c

# High-value opportunities (land value >$50K, improvement ratio <30%)
awk -F',' 'NR>1 && $47 && $48>50000 && $47/$48<0.3 {print $11,$47,$48,$45,$47}' CSV/Property_Information_20250716.csv
```

### Strategic Analysis Framework
When analyzing parcels, always cross-reference these three dimensions:
1. **Zoning District** - Current legal use rights and intensity limits
2. **FLU Category** - Long-term policy vision and density targets
3. **Character Area** - Design context and infrastructure expectations

## Project-Specific Conventions

### CSV Data Structure (Key Columns)
- **Column 11**: Full Address
- **Column 14**: Character Area (in Lot CSV)
- **Column 45**: Future Land Use category
- **Column 47**: Improvement Value (buildings/structures)
- **Column 48**: Land Value (raw land assessment)
- **Column 49**: Zoning District

### Zoning-FLU Compatibility Classifications
- **Compatible**: Current zoning aligns with FLU (e.g., A3.3 + CN, C5 + DC)
- **Under-intense**: Zoning allows less than FLU permits (upzoning opportunity)
- **Over-intense**: Zoning exceeds FLU vision (regulatory risk, may need conditions)
- **Incompatible**: Fundamental mismatch requiring plan amendment or relocation

### High-Value Zoning-FLU Mismatch Opportunities

#### Under-Intense Residential (Prime Upzoning Targets)
- **A2 (5.8 du/ac) + CN FLU**: CN allows 8-20 du/ac with townhomes/small multi-family - upzone to A2.5/A3.1
- **A2.7 (7.3 du/ac) + CN FLU**: CN supports up to 20 du/ac - upzone to A3.1 (11.5 du/ac) or A3.2 (17.4 du/ac)
- **A3.1 (11.5 du/ac) + UN FLU**: UN requires >20 du/ac with mixed-use - upzone to A3.2 (17.4 du/ac) or A3.3 (29 du/ac)
- **A2/A2.7 + UN FLU**: Massive under-intensity - UN demands >20 du/ac mixed-use, current zoning <8 du/ac

#### Commercial Under-Intensity (Revenue Enhancement)
- **NC (neighborhood commercial) + RC FLU**: RC supports regional-scale retail/employment centers - upzone to LC2/LC3/HC1
- **LC1 (15K sq ft max) + RC FLU**: RC allows multi-story mixed-use centers - upzone to LC3/HC1/GOL combo
- **A3.x + MU FLU**: Mixed-Use FLU encourages commercial integration - add commercial overlay or rezone to mixed-use

#### Downtown/Urban Core Mismatches (Highest ROI)
- **LC1/LC2 + DC FLU**: Downtown Core supports unlimited height/intensity - upzone to C5 or GOH
- **A3.x + DC FLU**: Downtown should be mixed-use towers - rezone to C5 with residential component
- **NC + DC FLU**: Neighborhood commercial in downtown core - major upzone opportunity to C5/GOH

#### Character Area Conflicts (Regulatory Risk)
- **A4 (43.6 du/ac) outside Urban/Walkable Character Area**: Code requires A4 be in Urban/Walkable or Regional Centers only
- **High-intensity commercial in Rural Character Area**: HC1/HC2 inappropriate for rural context
- **C5 outside Downtown Character Area**: Downtown zoning without downtown infrastructure/context

#### Transit-Oriented Opportunities (BRT Corridor Focus)
- **A2/A2.7 along Plank Road BRT**: Transit corridor should support A3.2+ density (17.4+ du/ac)
- **LC1 near BRT stations**: Transit nodes warrant LC2/LC3 intensity with reduced parking
- **Single-family near major transit**: Prime candidates for mixed-use or higher-density residential

#### Financial Analysis Targeting
```bash
# Find A2/A2.7 parcels with CN or UN FLU (upzoning candidates)
awk -F',' 'NR>1 && ($49=="A2" || $49=="A2.7") && ($45=="CN" || $45=="UN") {print $11,$49,$45,$47,$48}' CSV/Property_Information_20250716.csv

# Locate downtown parcels with non-C5/GOH zoning (major upzone potential)
awk -F',' 'NR>1 && $45=="DC" && $49!="C5" && $49!="GOH" {print $11,$49,$45,$47,$48}' CSV/Property_Information_20250716.csv

# Character area violations (A4 outside proper areas)
grep -E "A4.*Rural|A4.*Suburban" CSV/Property_Information_20250716.csv
```

### Critical Entitlement Process
1. **Pre-application conference** with Planning Director (required by UDC)
2. **Community engagement** via Build Baton Rouge corridor plans
3. **Planning Commission** public hearing (simple majority for approval)
4. **Metropolitan Council** final action (2/3 majority if Planning Commission denies)

### File Organization Logic
- Strategy documents use **British spelling** and em-dashes (strategic consulting format)
- CSV data follows **EBR GIS naming conventions** with dated suffixes (YYYYMMDD format)
- Focus on **parcels with improvement value < land value** as primary development targets
- All monetary analyses use actual assessed values from parish assessor data

When working with this codebase, prioritize understanding the **zoning-FLU-character area intersection** as it drives all investment decisions and regulatory strategies. Always validate data patterns with terminal commands before making analytical conclusions.
