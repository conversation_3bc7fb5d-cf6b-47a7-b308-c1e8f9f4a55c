# Claude Code - Sub Agents

Source: https://docs.anthropic.com/en/docs/claude-code/sub-agents

---

Custom sub agents in Claude Code are specialized AI assistants that can be invoked to handle specific types of tasks. They enable more efficient problem-solving by providing task-specific configurations with customized system prompts, tools and a separate context window. What are sub agents? Sub agents are pre-configured AI personalities that <PERSON> can delegate tasks to. Each sub agent:
Has a specific purpose and expertise area
Uses its own context window separate from the main conversation
Can be configured with specific tools it’s allowed to use
Includes a custom system prompt that guides its behavior When <PERSON> encounters a task that matches a sub agent’s expertise, it can delegate that task to the specialized sub agent, which works independently and returns results. Key benefits Quick start To create your first sub agent: 1 2 3 4 Sub agent configuration File locations Sub agents are stored as Markdown files with YAML frontmatter in two possible locations: Type Location Scope Priority Project sub agents .claude`/agents`/ Available in current project Highest User sub agents ~/.claude`/agents`/ Available across all projects Lower When sub agent names conflict, project-level sub agents take precedence over user-level sub agents. File format Each sub agent is defined in a Markdown file with this structure: Configuration fields Field Required Description name Yes Unique identifier using lowercase letters and hyphens description Yes Natural language description of the sub agent’s purpose tools No Comma-separated list of specific tools. If omitted, inherits all tools from the main thread Available tools Sub agents can be granted access to any of Claude Code’s internal tools. See the tools documentation for a complete list of available tools. You have two options for configuring tools:
Omit the tools field to inherit all tools from the main thread (default), including MCP tools
Specify individual tools as a comma-separated list for more granular control (can be edited manually or via /agents) MCP Tools: Sub agents can access MCP tools from configured MCP servers. When the tools field is omitted, sub agents inherit all MCP tools available to the main thread. Managing sub agents Using the /agents command (Recommended) The /agents command provides a comprehensive interface for sub agent management: This opens an interactive where you can: available sub agents (built-in, user, and project)

## Create new sub agents with guided setup

Edit existing custom sub agents, including their tool access
Delete custom sub agents
See which sub agents are active when duplicates exist
Easily manage tool permissions with a complete list of available tools Direct file management You can also manage sub agents by working directly with their files: Using sub agents effectively Automatic delegation Claude Code proactively delegates tasks based on:
The task description in your request
The description field in sub agent configurations
Current context and available tools Explicit invocation Request a specific sub agent by mentioning it in your command: Example sub agents Code reviewer Debugger Data scientist Best practices
Start with Claude-generated agents: We highly recommend generating your initial sub agent with Claude and then iterating on it to make it personally yours. This approach gives you the best results - a solid foundation that you can customize to your specific needs.
Design focused sub agents: Create sub agents with single, clear responsibilities rather than trying to make one sub agent do everything. This improves performance and makes sub agents more predictable.
Write detailed prompts: Include specific instructions, examples, and constraints in your system prompts. The more guidance you provide, the better the sub agent will perform.
Limit tool access: Only grant tools that are necessary for the sub agent’s purpose. This improves security and helps the sub agent focus on relevant actions.
Version control: Check project sub agents into version control so your team can benefit from and improve them collaboratively. Advanced usage Chaining sub agents For complex workflows, you can chain multiple sub agents: Dynamic sub agent selection Claude Code intelligently selects sub agents based on context. Make your description fields specific and action-oriented for best results. Performance considerations
Context efficiency: Agents help preserve main context, enabling longer overall sessions
Latency: Sub agents start off with a clean slate each time they are invoked and may add latency as they gather context that they require to do their job effectively.
Slash commands - Learn other built-in commands
Settings - Configure Claude Code behavior
Hooks - Automate workflows with event handlers