# Claude Code - Ide Integrations

Source: https://docs.anthropic.com/en/docs/claude-code/ide-integrations

---

<PERSON> works great with any Integrated Development Environment (IDE) that has a terminal. Just run claude, and you’re ready to go. In addition, Claude Code provides dedicated integrations for popular IDEs, which provide features like interactive diff viewing, selection context sharing, and more. These integrations currently exist for:
Visual Studio Code (including popular forks like Cursor, Windsurf, and VSCodium)
JetBrains IDEs (including IntelliJ, PyCharm, Android Studio, WebStorm, PhpStorm and GoLand) Features
Quick launch: Use Cmd+Esc (Mac) or Ctrl+Esc (Windows/Linux) to open Claude Code directly from your editor, or click the Claude Code button in the UI
Diff viewing: Code changes can be displayed directly in the IDE diff viewer instead of the terminal. You can configure this in /config
Selection context: The current selection`/tab` in the IDE is automatically shared with Claude Code
File reference shortcuts: Use Cmd+Option+K (Mac) or Alt+Ctrl+K (Linux/Windows) to insert file references (e.g., @File#L1-99)
Diagnostic sharing: Diagnostic errors (lint, syntax, etc.) from the IDE are automatically shared with <PERSON> as you work Installation To install Claude Code on VS Code and popular forks like Cursor, Windsurf, and VSCodium:

## Open VS Code
Open the integrated terminal

Run claude - the extension will auto-install Usage From your IDE Run `claude from` your IDE’s integrated terminal, and all features will be active. From external terminals Use the /ide command in any external terminal to connect Claude Code to your IDE and activate all features. If you want Claude to have access to the same files as your IDE, start Claude Code from the same directory as your IDE project root. Configuration IDE integrations work with Claude Code’s configuration system:

## Run `claude

Enter` the /config command
Adjust your preferences. Setting the diff tool to auto will enable automatic IDE detection Troubleshooting VS Code extension not installing
Ensure you’re running Claude Code from VS Code’s integrated terminal
Ensure that the CLI corresponding to your IDE is installed:
For VS Code: code command should be available
For Cursor: cursor command should be available
For Windsurf: windsurf command should be available
For VSCodium: codium command should be available
If not installed, use Cmd+Shift+P (Mac) or Ctrl+Shift+P (Windows/Linux) and search for “Shell **Command:** Install ‘code’ command in PATH” (or the equivalent for your IDE)
Check that VS Code has permission to install extensions JetBrains plugin not working
Ensure you’re running Claude Code from the project root directory
Check that the JetBrains plugin is enabled in the IDE settings
Completely restart the IDE. You may need to do this multiple times
For JetBrains Remote Development, ensure that the Claude Code plugin is installed in the remote host and not locally on the client For additional help, refer to our troubleshooting guide.