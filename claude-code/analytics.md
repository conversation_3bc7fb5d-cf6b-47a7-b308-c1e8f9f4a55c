# Claude Code - Analytics

Source: https://docs.anthropic.com/en/docs/claude-code/analytics

---

<PERSON> provides an analytics dashboard that helps organizations understand developer usage patterns, track productivity metrics, and optimize their Claude Code adoption. Access analytics Navigate to the analytics dashboard at console.`anthropic.com``/claude`\_code. Required roles

## Primary Owner
Owner
Billing
Admin

Developer Available metrics Lines of code accepted Total lines of code written by Claude <PERSON> that users have accepted in their sessions.

## Excludes rejected code suggestions

Doesn’t track subsequent deletions Suggestion accept rate Percentage of times users accept code editing tool usage, including:

## Edit
MultiEdit
Write

NotebookEdit Activity users: Number of active users in a given day (number on left Y-axis) sessions: Number of active sessions in a given day (number on right Y-axis) Spend users: Number of active users in a given day (number on left Y-axis) spend: Total dollars spent in a given day (number on right Y-axis) Team insights Members: All users who have authenticated to Claude Code

## API key users are displayed by API key identifier

OAuth users are displayed by email address Avg daily spend: Per-user average spend for the current month. For example, on July 10, this reflects the average daily spend over 10 days. Avg lines`/day`: Per-user average of accepted code lines for the current month. Using analytics effectively Monitor adoption Track team member status to identify:

## Active users who can share best practices

Overall adoption trends across your organization Measure productivity Tool acceptance rates and code metrics help you:
Understand developer satisfaction with Claude Code suggestions
Track code generation effectiveness
Identify opportunities for training or process improvements
Monitoring usage with OpenTelemetry for custom metrics and alerting
Identity and access management for role configuration