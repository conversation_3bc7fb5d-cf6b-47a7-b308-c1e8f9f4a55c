# Claude Code - Common Workflows

Source: https://docs.anthropic.com/en/docs/claude-code/common-workflows

---

Each task in this document includes clear instructions, example commands, and best practices to help you get the most from <PERSON>. Understand new codebases Get a quick codebase overview Suppose you’ve just joined a new project and need to understand its structure quickly. 1 2 3 4 Find relevant code Suppose you need to locate code related to a specific feature or functionality. 1 2 3
Fix bugs efficiently Suppose you’ve encountered an error message and need to find and fix its source. 1 2 3
Refactor code Suppose you need to update old code to use modern patterns and practices. 1 2 3 4
Use specialized sub agents Suppose you want to use specialized AI sub agents to handle specific tasks more effectively. 1 2 3 4
Work with tests Suppose you need to add tests for uncovered code. 1 2 3 4
Create pull requests Suppose you need to create a well-documented pull request for your changes. 1 2 3 4 Handle documentation Suppose you need to add or update documentation for your code. 1 2 3 4
Work with images Suppose you need to work with images in your codebase, and you want <PERSON>’s help analyzing image content. 1 2 3 4
Reference files and directories Use @ to quickly include files or directories without waiting for <PERSON> to read them. 1 2 3
Use extended thinking Suppose you’re working on complex architectural decisions, challenging bugs, or planning multi-step implementations that require deep reasoning. 1 2
Resume previous conversations Suppose you’ve been working on a task with <PERSON> and need to continue where you left off in a later session. <PERSON> Code provides two options for resuming previous conversations:
--continue to automatically continue the most recent conversation
--resume to display a conversation picker 1 2 3
Run parallel Claude Code sessions with Git worktrees Suppose you need to work on multiple tasks simultaneously with complete code isolation between Claude Code instances. 1 2 3 4 5
Use Claude as a unix-style utility Add Claude to your verification process Suppose you want to use Claude Code as a linter or code reviewer. Add Claude to your build script: Pipe in, pipe out Suppose you want to pipe data into Claude, and get back data in a structured format. Pipe data through Claude: Control output format Suppose you need Claude’s output in a specific format, especially when integrating Claude Code into scripts or other tools. 1 2 3
Create custom slash commands Claude Code supports custom slash commands that you can create to quickly execute specific prompts or tasks. For more details, see the Slash commands reference page. Create project-specific commands Suppose you want to create reusable slash commands for your project that all team members can use. 1 2 3 Add command arguments with $ARGUMENTS Suppose you want to create flexible slash commands that can accept additional input from users. 1 2 Create personal slash commands Suppose you want to create personal slash commands that work across all your projects. 1 2 3
Ask Claude its capabilities Claude has built-in access to its documentation and can answer questions its own features and limitations. Example questions
Next steps Claude Code reference implementation Clone our development container reference implementation.