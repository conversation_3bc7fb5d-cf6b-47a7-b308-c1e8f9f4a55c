# Claude Code - Security

Source: https://docs.anthropic.com/en/docs/claude-code/security

---

How we approach security Security foundation Your code’s security is paramount. Claude Code is built with security at its core, developed according to Anthropic’s comprehensive security program. and access resources (SOC 2 Type 2 report, ISO 27001 certificate, etc.) at Anthropic Trust Center. Permission-based architecture Claude Code uses strict read-only permissions by default. When additional actions are needed (editing files, running tests, executing commands), <PERSON> requests explicit permission. Users control whether to approve actions once or allow them automatically. We designed Claude Code to be transparent and secure. For example, we require approval for git commands before executing them, giving you direct control. This approach enables users and organizations to configure permissions directly. For detailed permission configuration, see Identity and Access Management. Built-in protections To mitigate risks in agentic systems:
Folder access restriction: Claude Code can only access the folder where it was started and its subfolders—it cannot go upstream to parent directories. This creates a clear security boundary, ensuring Claude Code only operates within the intended project scope
Prompt fatigue mitigation: Support for allowlisting frequently used safe commands per-user, per-codebase, or per-organization
Accept Edits mode: <PERSON>ch accept multiple edits while maintaining permission prompts for commands with side effects User responsibility Claude <PERSON> only has the permissions you grant it. You’re responsible for reviewing proposed code and commands for safety before approval. Protect against prompt injection Prompt injection is a technique where an attacker attempts to override or manipulate an AI assistant’s instructions by inserting malicious text. <PERSON> Code includes several safeguards against these attacks: Core protections
Permission system: Sensitive operations require explicit approval
Context-aware analysis: Detects potentially harmful instructions by analyzing the full request
Input sanitization: Prevents command injection by processing user inputs
Command blocklist: Blocks risky commands that fetch arbitrary content from the web like curl and wget Additional safeguards
Network request approval: Tools that make network requests require user approval by default
Isolated context windows: Web fetch uses a separate context window to avoid injecting potentially malicious prompts
Trust verification: First-time codebase runs and new MCP servers require trust verification
Command injection detection: Suspicious bash commands require manual approval even if previously allowlisted
Fail-closed matching: Unmatched commands default to requiring manual approval
Natural language descriptions: Complex bash commands include explanations for user understanding
Secure credential storage: API keys and tokens are encrypted. See Credential Management Best practices for working with untrusted content:
Review suggested commands before approval
Avoid piping untrusted content directly to Claude
Verify proposed changes to critical files
Use virtual machines (VMs) to run scripts and make tool calls, especially when interacting with external web services
Report suspicious behavior with /bug MCP security Claude Code allows users to configure Model Context Protocol (MCP) servers. The list of allowed MCP servers is configured in your source code, as part of Claude Code settings engineers check into source control. We encourage either writing your own MCP servers or using MCP servers from providers that you trust. You are able to configure Claude Code permissions for MCP servers. Anthropic does not manage or audit any MCP servers. Security best practices Working with sensitive code

## Review all suggested changes before approval

Use project-specific permission settings for sensitive repositories
Consider using devcontainers for additional isolation
Regularly audit your permission settings with /permissions Team security
Use enterprise managed policies to enforce organizational standards
Share approved permission configurations through version control
Train team members on security best practices
Monitor Claude Code usage through OpenTelemetry metrics Reporting security issues If you discover a security vulnerability in Claude Code:
Do not disclose it publicly
Report it through our HackerOne program
Include detailed reproduction steps
Allow time for us to address the issue before public disclosure
Identity and Access Management - Configure permissions and access controls
Monitoring usage - Track and audit Claude Code activity
Development containers - Secure, isolated environments
Anthropic Trust Center - Security certifications and compliance