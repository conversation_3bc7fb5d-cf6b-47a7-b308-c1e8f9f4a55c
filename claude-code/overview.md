# Claude Code - Overview

Source: https://docs.anthropic.com/en/docs/claude-code/overview

---

Get started in 30 seconds **Prerequisites:** Node.js 18 or newer That’s it! You’re ready to start coding with <PERSON>. Continue with Quickstart (5 mins) → (Got specific setup needs or hit issues? See advanced setup or troubleshooting.) What Claude Code does for you
Build features from descriptions: Tell <PERSON> what you want to build in plain English. It will make a plan, write the code, and ensure it works.
Debug and fix issues: Describe a bug or paste an error message. Claude Code will analyze your codebase, identify the problem, and implement a fix.
Navigate any codebase: Ask anything your team’s codebase, and get a thoughtful answer back. <PERSON> maintains awareness of your entire project structure, can find up-to-date information from the web, and with MCP can pull from external datasources like Google Drive, Figma, and Slack.
Automate tedious tasks: Fix fiddly lint issues, resolve merge conflicts, and write release notes. Do all this in a single command from your developer machines, or automatically in CI. Why developers love Claude Code
Works in your terminal: Not another chat window. Not another IDE. <PERSON> meets you where you already work, with the tools you already love.
Takes action: <PERSON> can directly edit files, run commands, and create commits. Need more? MCP lets <PERSON> read your design docs in Google Drive, update your tickets in Jira, or use _your_ custom developer tooling.
Unix philosophy: Claude Code is composable and scriptable. tail -f `app.log` | `claude -p` "Slack me if you see any anomalies appear in this log stream" _works_. Your CI can run `claude -p` "If there are new text strings, translate them into French and raise a PR for @lang-fr-team to review".
Enterprise-ready: Use Anthropic’s API, or host on AWS or GCP. Enterprise-grade security, , and compliance is built-in. Next steps Additional resources Was this page helpful? On this page
Get started in 30 seconds
What Claude Code does for you
Why developers love Claude Code
Next steps
Additional resources