# Claude Code - Amazon Bedrock

Source: https://docs.anthropic.com/en/docs/claude-code/amazon-bedrock

---

Prerequisites Before configuring Claude Code with Bedrock, ensure you have:

## An AWS account with Bedrock access enabled

Access to desired Claude models (e.g., Claude Sonnet 4) in Bedrock
AWS CLI installed and configured (optional - only needed if you don’t have another mechanism for getting credentials)
Appropriate IAM permissions Setup 1\. Enable model access First, ensure you have access to the required Claude models in your AWS account:

## Navigate to the Amazon Bedrock console

Go to Model access in the left Request access to desired Claude models (e.g., Claude Sonnet 4)
Wait for approval (usually instant for most regions) 2\. Configure AWS credentials Claude <PERSON> uses the default AWS SDK credential chain. Set up your credentials using one of these methods: Option A: AWS CLI configuration Option B: Environment variables (access key) Option C: Environment variables (SSO profile) Option D: Bedrock API keys Bedrock API keys provide a simpler authentication method without needing full AWS credentials. Bedrock API keys. Advanced credential configuration Claude Code supports automatic credential refresh for AWS SSO and corporate identity providers. Add these settings to your Claude Code settings file (see Settings for file locations). When Claude Code detects that your AWS credentials are expired (either locally based on their timestamp or when Bedrock returns a credential error), it will automatically run your configured awsAuthRefresh and`/or` awsCredentialExport commands to obtain new credentials before retrying the request. Example configuration Configuration settings explained awsAuthRefresh: Use this for commands that modify the .aws directory (e.g., updating credentials, SSO cache, or config files). Output is shown to the user (but user input is not supported), making it suitable for browser-based authentication flows where the CLI displays a code to enter in the browser. awsCredentialExport: Only use this if you cannot modify .aws and must directly return credentials. Output is captured silently (not shown to the user). The command must output JSON in this format: 3\. Configure Claude Code Set the following environment variables to enable Bedrock: When enabling Bedrock for Claude Code, keep the following in mind:
AWS_REGION is a required environment variable. Claude Code does not read from the .aws config file for this setting.
When using Bedrock, the / and /logout commands are disabled since authentication is handled through AWS credentials.
You can use settings files for environment variables like AWS_PROFILE that you don’t want to leak to other processes. See Settings for more information. 4\. Model configuration Claude Code uses these default models for Bedrock: Model type Default value Primary model us.anthropic.claude-3-7-sonnet-20250219-v1:0 Small`/fast` model us.anthropic.claude-3-5-haiku-20241022-v1:0 To customize models, use one of these methods: 5\. Output token configuration When using Claude Code with Amazon Bedrock, we recommend the following token settings: Why these values:
CLAUDE_CODE_MAX_OUTPUT_TOKENS=4096: Bedrock’s burndown throttling logic sets a minimum of 4096 tokens as the max\_token penalty. Setting this lower won’t reduce costs but may cut off long tool uses, causing the Claude Code agent loop to fail persistently. Claude Code typically uses less than 4096 output tokens without extended thinking, but may need this headroom for tasks involving significant file creation or Write tool usage.
MAX_THINKING_TOKENS=1024: This provides space for extended thinking without cutting off tool use responses, while still maintaining focused reasoning chains. This balance helps prevent trajectory changes that aren’t always helpful for coding tasks specifically. IAM configuration Create an IAM policy with the required permissions for Claude Code: For more restrictive permissions, you can limit the Resource to specific inference profile ARNs. For details, see Bedrock IAM documentation. Troubleshooting If you encounter region issues:
Check model availability: aws bedrock list-inference-profiles --region your-region
Switch to a supported region: export AWS_REGION=us-east-1
Consider using inference profiles for cross-region access If you receive an error “on-demand throughput isn’t supported”:
Specify the model as an inference profile ID Additional resources
Bedrock documentation
Bedrock pricing
Bedrock inference profiles
Claude Code on Amazon Bedrock: Quick Setup Guide