# Claude Code - Iam

Source: https://docs.anthropic.com/en/docs/claude-code/iam

---

Authentication methods Setting up Claude Code requires access to Anthropic models. For teams, you can set up Claude Code access in one of three ways:
Anthropic API via the Anthropic Console
Amazon Bedrock
Google Vertex AI Anthropic API authentication To set up Claude Code access for your team via Anthropic API:
Use your existing Anthropic Console account or create a new Anthropic Console account
You can add users through either method below:
Bulk invite users from within the Console (Console -> Settings -> Members -> Invite)

## Set up SSO

When inviting users, they need one of the following roles:
“Claude Code” role means users can only create Claude Code API keys
“Developer” role means users can create any kind of API key
Each invited user needs to complete these steps:
Accept the Console invite
Check system requirements
Install Claude Code with Console account credentials Cloud provider authentication To set up Claude Code access for your team via Bedrock or Vertex:

## Follow the Bedrock docs or Vertex docs

Distribute the environment variables and instructions for generating cloud credentials to your users. how to manage configuration here.
Users can install Claude Code Access control and permissions We support fine-grained permissions so that you’re able to specify exactly what the agent is allowed to do (e.g. run tests, run linter) and what it is not allowed to do (e.g. update cloud infrastructure). These permission settings can be checked into version control and distributed to all developers in your organization, as well as customized by individual developers. Permission system Claude Code uses a tiered permission system to balance power and safety: Tool Type Example Approval Required ”Yes, don’t ask again” Behavior Read-only File reads, LS, Grep No N/A Bash Commands Shell execution Yes Permanently per project directory and command File Modification Edit`/write` files Yes Until session end Configuring permissions You can view & manage Claude Code’s tool permissions with /permissions. This UI lists all permission rules and the `settings.json` file they are sourced from.
Allow rules will allow Claude Code to use the specified tool without further manual approval.
Deny rules will prevent Claude Code from using the specified tool. Deny rules take precedence over allow rules.
Additional directories extend Claude’s file access to directories beyond the initial working directory.
Default mode controls Claude’s permission behavior when encountering new requests. Permission rules use the format: Tool or Tool(optional-specifier) A rule that is just the tool name matches any use of that tool. For example, adding Bash to the list of allow rules would allow Claude Code to use the Bash tool without requiring user approval. Permission modes Claude Code supports several permission modes that can be set as the defaultMode in settings files: Mode Description default Standard behavior - prompts for permission on first use of each tool acceptEdits Automatically accepts file edit permissions for the session plan Plan mode - Claude can analyze but not modify files or execute commands bypassPermissions Skips all permission prompts (requires safe environment - see warning below) Working directories By default, Claude has access to files in the directory where it was launched. You can extend this access:
During startup: Use --add-dir <path> CLI argument
During session: Use /add-dir slash command
Persistent configuration: Add to additionalDirectories in settings files Files in additional directories follow the same permission rules as the original working directory - they become readable without prompts, and file editing permissions follow the current permission mode. Tool-specific permission rules Some tools support more fine-grained permission controls: Bash
Bash(npm run build) Matches the exact Bash command npm run build
Bash(npm run test:*) Matches Bash commands starting with npm run test. Read & Edit Edit rules apply to all built-in tools that edit files. Claude will make a best-effort attempt to apply Read rules to all built-in tools that read files like Grep, Glob, and LS. Read & Edit rules both follow the gitignore specification. Patterns are resolved relative to the directory containing .claude`/settings`.json. To reference an absolute path, use //. For a path relative to your directory, use ~/.
Edit(docs/) Matches edits to files in the docs directory of your project
Read(~/.zshrc) Matches reads to your ~/.zshrc file
Edit(//tmp`/scratch`.txt) Matches edits to /tmp`/scratch`.txt WebFetch
WebFetch(domain:`example.com`) Matches fetch requests to `example.com` MCP
mcp__puppeteer Matches any tool provided by the puppeteer server (name configured in Claude Code)
mcp__puppeteer__puppeteer_navigate Matches the puppeteer_navigate tool provided by the puppeteer server Additional permission control with hooks Claude Code hooks provide a way to custom shell commands to perform permission evaluation at runtime. When Claude Code makes a tool call, PreToolUse hooks run before the permission system runs, and the hook output can determine whether to approve or deny the tool call in place of the permission system. Enterprise managed policy settings For enterprise deployments of Claude Code, we support enterprise managed policy settings that take precedence over user and project settings. This allows system administrators to enforce security policies that users cannot override. System administrators can deploy policies to:
macOS: /Library/Application Support/ClaudeCode`/managed`-`settings.json`
Linux and WSL: /etc`/claude`-code`/managed`-`settings.json`
Windows: C:\ProgramData\ClaudeCode\managed-`settings.json` These policy files follow the same format as regular settings files but cannot be overridden by user or project settings. This ensures consistent security policies across your organization. Settings precedence When multiple settings sources exist, they are applied in the following order (highest to lowest precedence):

## Enterprise policies
Command line arguments

Local project settings (.claude`/settings`.`local.json`)
Shared project settings (.claude`/settings`.json)
User settings (~/.claude`/settings`.json) This hierarchy ensures that organizational policies are always enforced while still allowing flexibility at the project and user levels where appropriate. Credential management Claude Code securely manages your authentication credentials:
Storage location: On macOS, API keys, OAuth tokens, and other credentials are stored in the encrypted macOS Keychain.
Supported authentication types: Claude.ai credentials, Anthropic API credentials, Bedrock Auth, and Vertex Auth.
Custom credential scripts: The apiKeyHelper setting can be configured to run a shell script that returns an API key.
Refresh intervals: By default, apiKeyHelper is called after 5 minutes or on HTTP 401 response. Set CLAUDE_CODE_API_KEY_HELPER_TTL_MS environment variable for custom refresh intervals.