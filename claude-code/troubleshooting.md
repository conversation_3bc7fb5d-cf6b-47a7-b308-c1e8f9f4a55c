# Claude Code - Troubleshooting

Source: https://docs.anthropic.com/en/docs/claude-code/troubleshooting

---

Common installation issues Windows installation issues: errors in WSL You might encounter the following issues in WSL: OS`/platform` detection issues: If you receive an error during installation, WSL may be using Windows npm. Try:

## Run npm config set os linux before installation

Install with npm install -g @anthropic-ai`/claude`-code --force --no-os-check (Do NOT use sudo) Node not found errors: If you see exec: node: not found when running claude, your WSL environment may be using a Windows installation of Node.js. You can confirm this with which npm and which node, which should point to Linux paths starting with /usr/ rather than /mnt`/c`/. To fix this, try installing Node via your Linux distribution’s package manager or via nvm. Linux installation issues: permission errors When installing Claude Code with npm, you may encounter permission errors if your npm global prefix is not user writable (eg. /usr, or /usr`/local`). Recommended solution: Migrate to local installation The simplest solution is to migrate to a local installation: This moves Claude Code to ~/.claude`/local`/ and sets up an alias in your shell configuration. No sudo is required for future updates. After migration, restart your shell, and then verify your installation: Alternative solution: Create a user-writable npm prefix for global installs You can configure npm to use a directory within your folder: This solution:

## Avoids modifying system directory permissions

Creates a clean, dedicated location for your global npm packages
Follows security best practices System Recovery: If you have run commands that change ownership and permissions of system files or similar If you’ve already run a command that changed system directory permissions (such as sudo chown -R $USER:$(id -gn) /usr && sudo chmod -R u+w /usr) and your system is now broken (for example, if you see sudo: /usr`/bin``/sudo` must be owned by uid 0 and have the setuid bit set), you’ll need to perform recovery steps. Ubuntu/Debian Recovery Method:
While rebooting, hold SHIFT to access the GRUB Select “Advanced options for Ubuntu/Debian”

## Choose the recovery mode option

Select “Drop to root shell prompt”
Remount the filesystem as writable:
Fix permissions:
Reinstall affected packages (optional but recommended):
Reboot: Alternative Live USB Recovery Method: If the recovery mode doesn’t work, you can use a live USB:
Boot from a live USB (Ubuntu, Debian, or any Linux distribution)
Find your system partition:
Mount your system partition:
If you have a separate boot partition, mount it too:
Chroot into your system:
Follow steps 6-8 from the Ubuntu/Debian recovery method above After restoring your system, follow the recommended solution above to set up a user-writable npm prefix. Auto-updater issues If Claude Code can’t update automatically (see Update Claude Code for how updates work): For permission errors This is typically due to permission issues with your npm global prefix directory. You have several options:
Migrate to local installation (recommended): Run `claude migrate-installer` to move to a local installation that avoids permission issues entirely
Update manually: Run `claude update` with appropriate permissions
Fix npm permissions: Follow the recommended solution above (more complex) To disable auto-updates If you prefer to control when Claude Code updates: To check your installation
Current version and diagnostics: Run `claude doctor`
Check for updates: Run `claude update`
View update settings: Run `claude config` get autoUpdates --global
Verify installation location: Run which claude - if this shows an alias pointing to ~/.claude`/local``/claude`, you’re using the recommended local installation Permissions and authentication Repeated permission prompts If you find yourself repeatedly approving the same commands, you can allow specific tools to run without approval using the /permissions command. See Permissions docs. Authentication issues If you’re experiencing authentication problems:
Run /logout to sign out completely

## Close Claude Code

Restart with `claude and` complete the authentication process again If problems persist, try: This removes your stored authentication information and forces a clean . Performance and stability High CPU or memory usage Claude Code is designed to work with most development environments, but may consume significant resources when processing large codebases. If you’re experiencing performance issues:
Use /compact regularly to reduce context size

## Close and restart Claude Code between major tasks

Consider adding large build directories to your .gitignore file Command hangs or freezes If Claude Code seems unresponsive:
Press Ctrl+C to attempt to cancel the current operation
If unresponsive, you may need to close the terminal and restart ESC key not working in JetBrains (IntelliJ, PyCharm, etc.) terminals If you’re using Claude Code in JetBrains terminals and the ESC key doesn’t interrupt the agent as expected, this is likely due to a keybinding clash with JetBrains’ default shortcuts. To fix this issue:
Go to Settings → Tools → Terminal
Click the “Configure terminal keybindings” hyperlink next to “Override IDE Shortcuts”
Within the terminal keybindings, scroll down to “Switch focus to Editor” and delete that shortcut This will allow the ESC key to properly function for canceling Claude Code operations instead of being captured by PyCharm’s “Switch focus to Editor” action. Getting more help If you’re experiencing issues not covered here:
Use the /bug command within Claude Code to report problems directly to Anthropic

## Check the GitHub repository for known issues

Run /doctor to check the health of your Claude Code installation
Ask Claude directly its capabilities and features - Claude has built-in access to its documentation