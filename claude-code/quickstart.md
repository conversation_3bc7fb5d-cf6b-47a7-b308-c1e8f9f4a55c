# Claude Code - Quickstart Guide

Source: https://docs.anthropic.com/en/docs/claude-code/quickstart

---

This quickstart guide will have you using AI-powered coding assistance in just a few minutes. By the end, you’ll understand how to use Claude Code for common development tasks. Before you begin Make sure you have:

## A terminal or command prompt open

Node.js 18 or newer installed
A code project to work with **Step 1:** Install Claude Code To install Claude Code, run the following command: **Step 2:** Start your first session Open your terminal in any project directory and start Claude Code: You’ll see the Claude Code prompt inside a new interactive session: **Step 3:** Ask your first question Let’s start with understanding your codebase. Try one of these commands: <PERSON> will analyze your files and provide a summary. You can also ask more specific questions: You can also ask <PERSON> its own capabilities: **Step 4:** Make your first code change Now let’s make Claude Code do some actual coding. Try a simple task: Claude Code will:
Find the appropriate file
Show you the proposed changes
Ask for your approval
Make the edit **Step 5:** Use Git with Claude Code Claude Code makes Git operations conversational: You can also prompt for more complex Git operations: **Step 6:** Fix a bug or add a feature <PERSON> is proficient at debugging and feature implementation. Describe what you want in natural language: Or fix existing issues: Claude Code will:
Locate the relevant code
Understand the context
Implement a solution
Run tests if available **Step 7:** Test out other common workflows There are a number of ways to work with Claude: Refactor code Write tests Update documentation Code review Essential commands Here are the most important commands for daily use: Command What it does Example `claude Start` interactive mode `claude claude` "task" Run a one-time task `claude "fix` the build error" `claude -p` "query" Run one-off query, then exit `claude -p` "explain this function" `claude -c` Continue most recent conversation `claude -c` `claude -r` Resume a previous conversation `claude -r` `claude commit` Create a Git commit `claude commit` /clear Clear conversation history
/clear /help Show available commands
/help exit or Ctrl+C Exit Claude Code
exit See the CLI reference for a complete list of commands. Pro tips for beginners What’s next? Now that you’ve learned the basics, explore more advanced features: Getting help
In Claude Code: Type /help or ask “how do I…”
Documentation: You’re here! Browse other guides
Community: Join our Discord for tips and support