# Claude Code - Model Context Protocol (MCP)

Source: https://docs.anthropic.com/en/docs/claude-code/mcp

---

Model Context Protocol (MCP) is an open protocol that enables LLMs to access external tools and data sources. For more details MCP, see the MCP documentation. Configure MCP servers 1 2 3 4 Understanding MCP server scopes MCP servers can be configured at three different scope levels, each serving distinct purposes for managing server accessibility and sharing. Understanding these scopes helps you determine the best way to configure servers for your specific needs. Scope hierarchy and precedence MCP server configurations follow a clear precedence hierarchy. When servers with the same name exist at multiple scopes, the system resolves conflicts by prioritizing local-scoped servers first, followed by project-scoped servers, and finally user-scoped servers. This design ensures that personal configurations can override shared ones when needed. Local scope Local-scoped servers represent the default configuration level and are stored in your project-specific user settings. These servers remain private to you and are only accessible when working within the current project directory. This scope is ideal for personal development servers, experimental configurations, or servers containing sensitive credentials that shouldn’t be shared. Project scope Project-scoped servers enable team collaboration by storing configurations in a .`mcp.json` file at your project’s root directory. This file is designed to be checked into version control, ensuring all team members have access to the same MCP tools and services. When you add a project-scoped server, <PERSON> automatically creates or updates this file with the appropriate configuration structure. The resulting .`mcp.json` file follows a standardized format: For security reasons, <PERSON> Code prompts for approval before using project-scoped servers from .`mcp.json` files. If you need to reset these approval choices, use the `claude mcp` reset-project-choices command. User scope User-scoped servers provide cross-project accessibility, making them available across all projects on your machine while remaining private to your user account. This scope works well for personal utility servers, development tools, or services you frequently use across different projects. Choosing the right scope Select your scope based on:
Local scope: Personal servers, experimental configurations, or sensitive credentials specific to one project
Project scope: Team-shared servers, project-specific tools, or services required for collaboration
User scope: Personal utilities needed across multiple projects, development tools, or frequently-used services Environment variable expansion in .`mcp.json` Claude Code supports environment variable expansion in .`mcp.json` files, allowing teams to share configurations while maintaining flexibility for machine-specific paths and sensitive values like API keys. Supported syntax:
${VAR} - Expands to the value of environment variable VAR
${VAR:-default} - Expands to VAR if set, otherwise uses default Expansion locations: Environment variables can be expanded in:
command - The server executable path
args - Command-line arguments
env - Environment variables passed to the server
url - For SSE/HTTP server types
headers - For SSE/HTTP server authentication Example with variable expansion: If a required environment variable is not set and has no default value, Claude Code will fail to parse the config. Authenticate with remote MCP servers Many remote MCP servers require authentication. Claude Code supports OAuth 2.0 authentication flow for secure connection to these servers. 1 2 3 Connect to a Postgres MCP server Suppose you want to give Claude read-only access to a PostgreSQL database for querying and schema inspection. 1 2 Add MCP servers from JSON configuration Suppose you have a JSON configuration for a single MCP server that you want to add to Claude Code. 1 2 Import MCP servers from Claude Desktop Suppose you have already configured MCP servers in Claude Desktop and want to use the same servers in Claude Code without manually reconfiguring them. 1 2 3 Use Claude Code as an MCP server Suppose you want to use Claude Code itself as an MCP server that other applications can connect to, providing them with Claude’s tools and capabilities. 1 2 Use MCP resources MCP servers can expose resources that you can reference using @ mentions, similar to how you reference files. Reference MCP resources 1 2 3 Use MCP prompts as slash commands MCP servers can expose prompts that become available as slash commands in Claude Code. Execute MCP prompts 1 2 3