# Claude Code - Llm Gateway

Source: https://docs.anthropic.com/en/docs/claude-code/llm-gateway

---

LLM gateways provide a centralized proxy layer between Claude Code and model providers, offering:
Centralized authentication - Single point for API key management
Usage tracking - Monitor usage across teams and projects
Cost controls - Implement budgets and rate limits
Audit logging - Track all model interactions for compliance
Model routing - Switch between providers without code changes LiteLLM configuration Prerequisites
Claude Code updated to the latest version
LiteLLM Proxy Server deployed and accessible
Access to Claude models through your chosen provider Basic LiteLLM setup Configure Claude Code: Authentication methods Static API key Simplest method using a fixed API key: This value will be sent as the Authorization header. Dynamic API key with helper For rotating keys or per-user authentication:
Create an API key helper script:
Configure Claude Code settings to use the helper:
Set token refresh interval: This value will be sent as Authorization and X-Api-Key headers. The apiKeyHelper has lower precedence than ANTHROPIC_AUTH_TOKEN or ANTHROPIC_API_KEY. Unified endpoint (recommended) Using LiteLLM’s Anthropic format endpoint: Benefits of the unified endpoint over pass-through endpoints:

## Load balancing
Fallbacks

Consistent support for cost tracking and end-user tracking Provider-specific pass-through endpoints (alternative) Anthropic API through LiteLLM Using pass-through endpoint: Amazon Bedrock through LiteLLM Using pass-through endpoint: Google Vertex AI through LiteLLM Using pass-through endpoint: Model selection By default, the models will use those specified in Model configuration. If you have configured custom model names in LiteLLM, set the aforementioned environment variables to those custom names. For more detailed information, refer to the LiteLLM documentation. Additional resources
LiteLLM documentation
Claude Code settings
Corporate proxy setup
Third-party integrations overview