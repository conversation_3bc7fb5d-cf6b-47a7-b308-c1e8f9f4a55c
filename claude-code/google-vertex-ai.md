# Claude Code - Google Vertex Ai

Source: https://docs.anthropic.com/en/docs/claude-code/google-vertex-ai

---

Prerequisites Before configuring Claude Code with Vertex AI, ensure you have:
A Google Cloud Platform (GCP) account with billing enabled

## A GCP project with Vertex AI API enabled

Access to desired Claude models (e.g., Claude Sonnet 4)
Google Cloud SDK (gcloud) installed and configured
<PERSON><PERSON><PERSON> allocated in desired GCP region Setup 1\. Enable Vertex AI API Enable the Vertex AI API in your GCP project: 2\. Request model access Request access to Claude models in Vertex AI:

## Navigate to the Vertex AI Model Garden

Search for “Claude” models
Request access to desired Claude models (e.g., Claude Sonnet 4)
Wait for approval (may take 24-48 hours) 3\. Configure GCP credentials Claude <PERSON> uses standard Google Cloud authentication. For more information, see Google Cloud authentication documentation. 4\. Configure Claude Code Set the following environment variables: 5\. Model configuration Claude Code uses these default models for Vertex AI: Model type Default value Primary model claude-sonnet-4@******** Small`/fast` model claude-3-5-haiku@******** To customize models: IAM configuration Assign the required IAM permissions: The roles`/aiplatform`.user role includes the required permissions:
aiplatform.endpoints.predict - Required for model invocation
aiplatform.endpoints.computeTokens - Required for token counting For more restrictive permissions, create a custom role with only the permissions above. For details, see Vertex IAM documentation. Troubleshooting If you encounter quota issues:
Check current quotas or request quota increase through Cloud Console If you encounter “model not found” 404 errors:

## Verify you have access to the specified region

Confirm model is Enabled in Model Garden If you encounter 429 errors:
Ensure the primary model and small`/fast` model are supported in your selected region Additional resources
Vertex AI documentation
Vertex AI pricing
Vertex AI quotas and limits