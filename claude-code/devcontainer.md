# Claude Code - Devcontainer

Source: https://docs.anthropic.com/en/docs/claude-code/devcontainer

---

The preconfigured devcontainer setup works seamlessly with VS Code’s Remote - Containers extension and similar tools. The container’s enhanced security measures (isolation and firewall rules) allow you to run `claude --dangerously-skip-permissions` to bypass permission prompts for unattended operation. We’ve included a reference implementation that you can customize for your needs. Key features
Production-ready Node.js: Built on Node.js 20 with essential development dependencies
Security by design: Custom firewall restricting network access to only necessary services
Developer-friendly tools: Includes git, ZSH with productivity enhancements, fzf, and more
Seamless VS Code integration: Pre-configured extensions and optimized settings
Session persistence: Preserves command history and configurations between container restarts
Works everywhere: Compatible with macOS, Windows, and Linux development environments Getting started in 4 steps
Install VS Code and the Remote - Containers extension
Clone the Claude Code reference implementation repository
Open the repository in VS Code
When prompted, click “Reopen in Container” (or use Command Palette: Cmd+Shift+P → “Remote-Containers: Reopen in Container”) Configuration breakdown The devcontainer setup consists of three primary components:
`devcontainer.json`: Controls container settings, extensions, and volume mounts
Dockerfile: Defines the container image and installed tools
init-`firewall.sh`: Establishes network security rules Security features The container implements a multi-layered security approach with its firewall configuration:
Precise access control: Restricts outbound connections to whitelisted domains only (npm registry, GitHub, Anthropic API, etc.)
Allowed outbound connections: The firewall permits outbound DNS and SSH connections
Default-deny policy: Blocks all other external network access
Startup verification: Validates firewall rules when the container initializes
Isolation: Creates a secure development environment separated from your main system Customization options The devcontainer configuration is designed to be adaptable to your needs:
Add or remove VS Code extensions based on your workflow
Modify resource allocations for different hardware environments
Adjust network access permissions
Customize shell configurations and developer tooling Example use cases Secure client work Use devcontainers to isolate different client projects, ensuring code and credentials never mix between environments. Team onboarding New team members can get a fully configured development environment in minutes, with all necessary tools and settings pre-installed. Consistent CI/CD environments Mirror your devcontainer configuration in CI/CD pipelines to ensure development and production environments match.
VS Code devcontainers documentation
Claude Code security best practices
Corporate proxy configuration