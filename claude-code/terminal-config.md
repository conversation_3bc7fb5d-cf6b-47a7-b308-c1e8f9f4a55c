# Claude Code - Terminal Config

Source: https://docs.anthropic.com/en/docs/claude-code/terminal-config

---

Themes and appearance <PERSON> cannot control the theme of your terminal. That’s handled by your terminal application. You can match Claude <PERSON>’s theme to your terminal any time via the /config command. Line breaks You have several options for entering linebreaks into Claude Code:
Quick escape: Type \ followed by <PERSON><PERSON> to create a newline
Keyboard shortcut: Set up a keybinding to insert a newline Set up Shift+Enter (VS Code or iTerm2): Run /terminal-setup within Claude Code to automatically configure Shift+Enter. Set up Option+Enter (VS Code, iTerm2 or macOS Terminal.app): For Mac Terminal.app:
Open Settings → Profiles → Keyboard
Check “Use Option as Meta Key” For iTerm2 and VS Code terminal:
Open Settings → Profiles → Keys
Under General, set Left/Right Option key to “Esc+“ Notification setup Never miss when <PERSON> completes a task with proper notification configuration: Terminal bell notifications Enable sound alerts when tasks complete: For macOS users: Don’t forget to enable notification permissions in System Settings → Notifications → \[Your Terminal App\]. iTerm 2 system notifications For iTerm 2 alerts when tasks complete:
Open iTerm 2 Preferences
Navigate to Profiles → Terminal
Enable “Silence bell” and Filter Alerts → “Send escape sequence-generated alerts”
Set your preferred notification delay Note that these notifications are specific to iTerm 2 and not available in the default macOS Terminal. Custom notification hooks For advanced notification handling, you can create notification hooks to run your own logic. Handling large inputs When working with extensive code or long instructions:
Avoid direct pasting: Claude Code may struggle with very long pasted content
Use file-based workflows: Write content to a file and ask Claude to read it
Be aware of VS Code limitations: The VS Code terminal is particularly prone to truncating long pastes Vim Mode Claude Code supports a subset of Vim keybindings that can be enabled with /vim or configured via /config. The supported subset includes:
Mode switching: Esc (to NORMAL), i/I, a/A, o/O (to INSERT)
: h`/j``/k``/l`, w`/e``/b`, 0/$/^, gg/G
Editing: x, dw`/de``/db``/dd`/D, cw`/ce``/cb``/cc`/C, . (repeat)