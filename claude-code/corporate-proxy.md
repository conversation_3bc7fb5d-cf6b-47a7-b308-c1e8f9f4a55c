# Claude Code - Corporate Proxy

Source: https://docs.anthropic.com/en/docs/claude-code/corporate-proxy

---

<PERSON> supports standard HTTP/HTTPS proxy configurations through environment variables. This allows you to route all Claude Code traffic through your organization’s proxy servers for security, compliance, and monitoring purposes. Environment variables Claude Code respects standard proxy environment variables: Authentication Basic authentication If your proxy requires basic authentication, include credentials in the proxy URL: SSL certificate issues If your proxy uses custom SSL certificates, you may encounter certificate errors. Ensure that you set the correct certificate bundle path: Network access requirements Claude Code requires access to the following URLs:
api.`anthropic.com` - Claude API endpoints
statsig.`anthropic.com` - Telemetry and metrics
`sentry.io` - Error reporting Ensure these URLs are allowlisted in your proxy configuration and firewall rules. This is especially important when using Claude Code in containerized or restricted network environments. Additional resources
Claude Code settings
Environment variables reference
Troubleshooting guide