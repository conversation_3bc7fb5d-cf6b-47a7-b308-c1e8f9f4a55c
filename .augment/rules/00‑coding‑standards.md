---
type: "always_apply"
---

## Runtimes
- Enforce Python 3.13 (fallback 3.12), Node 22 LTS (optionally 24‑Current), TS 5.8 (auto‑track latest minor).

## Linting & Formatting
- Use Ruff ≥ 0.4 for lint and (optionally) formatting. Fallback to Black 24.x.
- Use Prettier 3.x (track latest minor) for JS/TS.
- CI must fail on any `ruff check --fix --format=grouped`, `black --check`, or `prettier --check` diff.

## Code Architecture
- Prefer functional core / imperative shell.
- Isolate side-effects behind interfaces.

## CI
- Fail CI if formatting or linting deviate from this standard.