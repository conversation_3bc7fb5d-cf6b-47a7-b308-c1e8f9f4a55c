---
type: "always_apply"
---

## Coverage & Test Performance

- All **new or modified logic** must ship with unit tests using `pytest` (for Python) or `vitest` (for TypeScript).
- Minimum **80 % branch coverage**, enforced at the **module level**, not globally.
- Tests must complete in under **5 seconds** unless explicitly marked as `@slow`.

## Property-Based Testing

- For any module containing **algorithmic logic** (e.g., zoning rule evaluation, geometry transformations, financial calculations), include at least **one `hypothesis`/`fast-check` test** that validates general properties and edge conditions.

## Contract & Snapshot Testing

- When calling **external APIs**, write **contract tests** that verify payload structure and status codes.
- For any **JSON-based output** (e.g., zoning analysis responses, GIS overlays), add **snapshot tests**.
- CI must **fail if snapshots drift**, unless updated via an explicit `--update-snapshots` flag and approved via PR description or label.

## CI Enforcements

- Use `pytest-cov` or `vitest --coverage` in CI. Fail the build if coverage or test performance thresholds aren’t met.
- Label flaky tests with `@flaky` and quarantine after >1 rerun.